import abc
from typing import Callable

import pandas as pd
import pandera as pa

from core_silver.observation_converter.converters.base_active_users_converter import (
    BaseActiveUsersConverter,
)
from data_sdk.domain.domain_types import ReportMetadata
from data_sdk.reports.schema import DailyActiveUsersConvertedReport


class BaseMonthlyActiveUsersConverter(BaseActiveUsersConverter, abc.ABC):
    """
    Base class for monthly active users converters.
    Handles monthly-specific logic like year/month parsing and column mapping.
    Now generates daily rows for each day in the date range.
    """

    converted_report_cls = DailyActiveUsersConvertedReport

    # Monthly-specific schema extends base schema with year/month fields
    _schema = pa.DataFrameSchema({
        **BaseActiveUsersConverter._base_schema_fields,
    })

    def _build_converted_dataframe(
        self, parsed_df: pd.DataFrame, metadata: ReportMetadata
    ) -> pd.DataFrame:
        """Build converted dataframe with daily rows for each day in date range."""
        # Generate date range from metadata.date_from to metadata.date_to
        date_range = pd.date_range(
            start=metadata.date_from, end=metadata.date_to, freq="D"
        )

        # Create a list to store all rows
        all_rows = []

        # For each row in the original parsed_df
        for _, row in parsed_df.iterrows():
            # Get common columns for this row (but pass single-row DataFrame)
            single_row_df = pd.DataFrame([row])
            common_columns = self._get_common_columns(single_row_df, metadata)

            # For each date in the range, create a new row
            for date in date_range:
                row_data = {}
                # Add common columns (extract values from Series/arrays)
                for key, value in common_columns.items():
                    if hasattr(value, "iloc"):
                        row_data[key] = value.iloc[0]
                    else:
                        row_data[key] = value

                # Add daily-specific columns
                row_data["date"] = date.strftime("%Y-%m-%d")
                row_data["daily_active_users"] = row["count"]

                all_rows.append(row_data)

        # Create DataFrame from all rows
        converted_df = pd.DataFrame(all_rows)
        return converted_df

    def _get_dtype_mapping(self) -> dict:
        return {
            **self._get_common_dtype_mapping(),
            "year": str,
            "month": str,
        }
