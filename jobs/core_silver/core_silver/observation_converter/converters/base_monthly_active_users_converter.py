import abc
from typing import Callable

import pandas as pd
import pandera as pa

from core_silver.observation_converter.converters.base_active_users_converter import (
    BaseActiveUsersConverter,
)
from data_sdk.domain.domain_types import ReportMetadata
from data_sdk.reports.schema import DailyActiveUsersConvertedReport


class BaseMonthlyActiveUsersConverter(BaseActiveUsersConverter, abc.ABC):
    """
    Base class for monthly active users converters.
    Handles monthly-specific logic like year/month parsing and column mapping.
    Now generates daily rows for each day in the date range.
    """

    converted_report_cls = DailyActiveUsersConvertedReport

    # Monthly-specific schema extends base schema with year/month fields instead of date
    _schema = pa.DataFrameSchema({
        "account": pa.Column(pa.String, coerce=True),
        "portal": pa.Column(pa.String, coerce=True),
        "year": pa.Column(pa.Int, coerce=True),
        "month": pa.Column(pa.Int, coerce=True),
        "product": pa.Column(pa.String, coerce=True),
        "product_id": pa.Column(pa.String, coerce=True),
        "count": pa.Column(pa.Int, coerce=True),
    })

    def _build_converted_dataframe(
        self, parsed_df: pd.DataFrame, metadata: ReportMetadata
    ) -> pd.DataFrame:
        """Build converted dataframe with daily rows for each day in date range."""
        # Generate date range from metadata.date_from to metadata.date_to
        date_range = pd.date_range(
            start=metadata.date_from, end=metadata.date_to, freq="D"
        )

        # Create DataFrame with dates
        dates_df = pd.DataFrame({
            "date": date_range,
            "key": 1,  # Key for cartesian product
        })

        # Add key column to parsed_df for cartesian product
        parsed_df_with_key = parsed_df.copy()
        parsed_df_with_key["key"] = 1

        # Perform cartesian product (cross join)
        expanded_df = parsed_df_with_key.merge(dates_df, on="key", how="outer")

        # Drop the key column
        expanded_df = expanded_df.drop("key", axis=1)

        # Get common columns using the expanded dataframe
        common_columns = self._get_common_columns(expanded_df, metadata)

        # Build the final dataframe
        converted_df = pd.DataFrame()
        converted_df = converted_df.assign(
            **common_columns,
            # Daily-specific columns
            date=expanded_df["date"].dt.strftime("%Y-%m-%d"),
            daily_active_users=expanded_df["count"],
        )

        return converted_df

    def _get_dtype_mapping(self) -> dict:
        return {
            **self._get_common_dtype_mapping(),
            "year": str,
            "month": str,
        }

    def _get_parse_dates(self) -> bool:
        """Return whether to parse dates during CSV extraction."""
        return False
